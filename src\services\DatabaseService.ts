
import { pool, executeQuery, testConnection } from '../configs/databaseConfig';

class DatabaseService {
  
  // Test database connection
  async testConnection(): Promise<boolean> {
    return await testConnection();
  }

  // Generic query execution
  async query(sql: string, params: any[] = []): Promise<any> {
    return await executeQuery(sql, params);
  }

  // Get all records from a table
  async findAll(tableName: string): Promise<any[]> {
    const sql = `SELECT * FROM ${tableName}`;
    return await this.query(sql);
  }

  // Find record by ID
  async findById(tableName: string, id: number): Promise<any> {
    const sql = `SELECT * FROM ${tableName} WHERE id = ?`;
    const results = await this.query(sql, [id]);
    return results[0] || null;
  }

  // Insert new record
  async insert(tableName: string, data: Record<string, any>): Promise<any> {
    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);
    
    const sql = `INSERT INTO ${tableName} (${columns}) VALUES (${placeholders})`;
    return await this.query(sql, values);
  }

  // Update record by ID
  async update(tableName: string, id: number, data: Record<string, any>): Promise<any> {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), id];
    
    const sql = `UPDATE ${tableName} SET ${setClause} WHERE id = ?`;
    return await this.query(sql, values);
  }

  // Delete record by ID
  async delete(tableName: string, id: number): Promise<any> {
    const sql = `DELETE FROM ${tableName} WHERE id = ?`;
    return await this.query(sql, [id]);
  }

  // Close connection pool
  async close(): Promise<void> {
    await pool.end();
  }
}

export const databaseService = new DatabaseService();
export default DatabaseService;
