
import mysql from 'mysql2/promise';

const dbConfig = {
  host: '**************',
  port: 3306,
  user: 'pcornejo',
  password: '<PERSON>cornej<PERSON>@2025',
  database: 'aunClick_prod',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // ssl: false, // SSL disabled
  acquireTimeout: 60000,
  timeout: 60000
};

// Create connection pool
export const pool = mysql.createPool(dbConfig);

// Test connection function
export const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('MySQL connection established successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('MySQL connection failed:', error);
    return false;
  }
};

// Helper function to execute queries
export const executeQuery = async (query: string, params: any[] = []) => {
  try {
    const [rows] = await pool.execute(query, params);
    return rows;
  } catch (error) {
    console.error('Query execution failed:', error);
    throw error;
  }
};

export default pool;
