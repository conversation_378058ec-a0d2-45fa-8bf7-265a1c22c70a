'use client';
import FusePageSimple from '@fuse/core/FusePageSimple';
import FuseLoading from '@fuse/core/FuseLoading';
import * as React from 'react';
import { useMemo, useState, useCallback } from 'react';
import { type MRT_ColumnDef } from 'material-react-table';
import DataTable from 'src/components/data-table/DataTable';
import {
  Paper,
  Typography,
  Card,
  CardContent,
  Box,
  Chip,
  Button,
  Alert,
  AlertTitle,
  CircularProgress
} from '@mui/material';
import { 
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { useDatabaseConnection } from '../../api/hooks/useDatabaseConnection';

/**
 * Componente header del dashboard de logística
 */
const LogisticsDashboardAppHeader = () => (
  <div className="flex flex-col sm:flex-row space-y-8 sm:space-y-0 w-full items-center justify-between py-24 px-24 md:px-32">
    <div className="flex flex-col items-center sm:items-start">
      <Typography
        component="h1"
        className="text-24 md:text-32 font-semibold tracking-tight leading-none text-gray-900 dark:text-gray-100"
      >
        Dashboard de Logística
      </Typography>
      <Typography
        component="h2"
        className="text-14 font-medium tracking-tight text-gray-500 dark:text-gray-400 mt-8"
      >
        Monitoreo de conexiones y estado del sistema
      </Typography>
    </div>
    <div className="flex items-center space-x-8">
      <StorageIcon className="text-48 text-gray-400" />
    </div>
  </div>
);

/**
 * Componente de estado de conexión
 */
const ConnectionStatus = ({ 
  isConnected, 
  isLoading, 
  error, 
  lastChecked,
  message,
  onRetry 
}: {
  isConnected: boolean | null;
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
  message?: string;
  onRetry: () => void;
}) => {
  const getStatusColor = () => {
    if (isLoading) return 'info';
    if (isConnected) return 'success';
    if (isConnected === false) return 'error';
    return 'default';
  };

  const getStatusText = () => {
    if (isLoading) return 'Verificando...';
    if (isConnected) return 'Conectado';
    if (isConnected === false) return 'Desconectado';
    return 'Sin verificar';
  };

  const getStatusIcon = () => {
    if (isLoading) return <CircularProgress size={16} />;
    if (isConnected) return <CheckCircleIcon />;
    return <ErrorIcon />;
  };

  return (
    <Card className="w-full">
      <CardContent className="p-24">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h3" className="font-semibold">
            Estado de Conexión MySQL
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={onRetry}
            disabled={isLoading}
          >
            Verificar
          </Button>
        </Box>
        
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          <Chip
            icon={getStatusIcon()}
            label={getStatusText()}
            color={getStatusColor() as any}
            variant={isConnected ? "filled" : "outlined"}
          />
          {lastChecked && (
            <Typography variant="body2" color="text.secondary">
              Última verificación: {lastChecked.toLocaleTimeString()}
            </Typography>
          )}
        </Box>

        {error && (
          <Alert severity="error" className="mt-16">
            <AlertTitle>Error de Conexión</AlertTitle>
            {error}
          </Alert>
        )}

        {isConnected && (
          <Alert severity="success" className="mt-16">
            <AlertTitle>Conexión Exitosa</AlertTitle>
            {message || 'La conexión a la base de datos MySQL se ha establecido correctamente.'}
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Componente de información de configuración
 */
const DatabaseInfo = () => (
  <Card className="w-full">
    <CardContent className="p-24">
      <Typography variant="h6" component="h3" className="font-semibold mb-16">
        Configuración de Base de Datos
      </Typography>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-16">
        <div>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <StorageIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">Host:</Typography>
          </Box>
          <Typography variant="body1">**************:3306</Typography>
        </div>
        
        <div>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <StorageIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary">Base de Datos:</Typography>
          </Box>
          <Typography variant="body1">aunClick_prod</Typography>
        </div>
        
        <div>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <Typography variant="body2" color="text.secondary">Usuario:</Typography>
          </Box>
          <Typography variant="body1">pcornejo</Typography>
        </div>
        
        <div>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <Typography variant="body2" color="text.secondary">Pool de Conexiones:</Typography>
          </Box>
          <Typography variant="body1">Máximo 10 conexiones</Typography>
        </div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Vista principal del Dashboard de Logística
 */
function LogisticsDashboardAppView() {
  const {
    isConnected,
    isLoading,
    error,
    lastChecked,
    message,
    connectionDetails,
    retry,
    modelData,
    isLoadingModelData,
    fetchModelData
  } = useDatabaseConnection();

  // Definir columnas para la tabla de modelos
  const modelColumns = useMemo<MRT_ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'id',
        header: 'ID',
        size: 80,
      },
      {
        accessorKey: 'nombre',
        header: 'Nombre',
      },
      {
        accessorKey: 'version',
        header: 'Versión',
        size: 100,
      },
      {
        accessorKey: 'estado',
        header: 'Estado',
        Cell: ({ row }) => (
          <Chip
            label={row.original.estado}
            color={
              row.original.estado === 'Activo' 
                ? 'success' 
                : row.original.estado === 'Inactivo' 
                  ? 'error' 
                  : 'warning'
            }
            size="small"
            variant="outlined"
          />
        ),
      },
      {
        accessorKey: 'fecha_creacion',
        header: 'Fecha Creación',
        size: 150,
      },
    ],
    []
  );

  // Mostrar loading inicial
  if (isLoading && isConnected === null) {
    return <FuseLoading />;
  }

  return (
    <FusePageSimple
      header={<LogisticsDashboardAppHeader />}
      content={
        <div className="w-full p-24">
          <div className="grid grid-cols-1 gap-24 max-w-6xl mx-auto">
            <ConnectionStatus
              isConnected={isConnected}
              isLoading={isLoading}
              error={error}
              lastChecked={lastChecked}
              message={message}
              onRetry={retry}
            />
            <DatabaseInfo />
            
            {/* Tabla de Modelos Seni Junior */}
            {isConnected && (
              <Card className="w-full">
                <CardContent className="p-24">
                  <Typography variant="h6" component="h3" className="font-semibold mb-16">
                    Tabla tb_modelo_seni_junior
                  </Typography>
                  
                  {isLoadingModelData ? (
                    <Box display="flex" justifyContent="center" p={4}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <DataTable
                      data={modelData || []}
                      columns={modelColumns}
                      renderTopToolbarCustomActions={() => (
                        <Button
                          variant="contained"
                          size="small"
                          startIcon={<RefreshIcon />}
                          onClick={() => fetchModelData()}
                          disabled={isLoadingModelData}
                          className="flex shrink ltr:mr-2 rtl:ml-2"
                          color="secondary"
                        >
                          Actualizar datos
                        </Button>
                      )}
                    />
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      }
    />
  );
}

export default LogisticsDashboardAppView;