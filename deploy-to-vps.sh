#!/bin/bash

# Script para actualizar Fuse React en el VPS desde GitHub
# Uso: ./deploy-to-vps.sh

echo "🔄 Actualizando Fuse React desde GitHub..."

# Configuración
VPS_HOST="**************"
VPS_PORT="22222"
VPS_USER="root"
VPS_PASSWORD="Tata2024*"
PROJECT_PATH="/var/www/fuse-react"

# Función para ejecutar comandos en el VPS
execute_remote() {
    sshpass -p "$VPS_PASSWORD" ssh -p $VPS_PORT -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "$1"
}

echo "📡 Conectando al VPS..."

# Detener el servidor de desarrollo si está corriendo
echo "⏹️ Deteniendo servidor..."
execute_remote "pkill -f 'vite --host' || true"

# Ir al directorio del proyecto y actualizar desde GitHub
echo "📥 Descargando cambios desde GitHub..."
execute_remote "cd $PROJECT_PATH && git fetch origin && git reset --hard origin/vitejs-demo && git clean -fd"

# Instalar/actualizar dependencias si es necesario
echo "📦 Verificando dependencias..."
execute_remote "cd $PROJECT_PATH && npm install --production=false"

# Reiniciar el servidor de desarrollo
echo "🚀 Iniciando servidor..."
execute_remote "cd $PROJECT_PATH && nohup NODE_OPTIONS='--max-old-space-size=1024' npm run dev > /var/log/fuse-react.log 2>&1 &"

# Esperar un momento para que el servidor inicie
sleep 5

# Verificar que el servidor esté funcionando
echo "🔍 Verificando servidor..."
if execute_remote "curl -s http://localhost:3000 > /dev/null"; then
    echo "✅ Actualización completada exitosamente!"
    echo "🌐 Aplicación disponible en: http://$VPS_HOST:3000/"
    echo "📋 Para ver logs: ssh -p $VPS_PORT $VPS_USER@$VPS_HOST 'tail -f /var/log/fuse-react.log'"
else
    echo "❌ Error: El servidor no está respondiendo"
    echo "📋 Revisar logs: ssh -p $VPS_PORT $VPS_USER@$VPS_HOST 'tail -20 /var/log/fuse-react.log'"
fi
