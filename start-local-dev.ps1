# Script para iniciar el servidor de desarrollo local
Write-Host "🔄 Iniciando servidor de desarrollo local..." -ForegroundColor Green

# Detener procesos que puedan estar usando el puerto 3001
Write-Host "⏹️ Deteniendo procesos en puerto 3001..." -ForegroundColor Yellow
$processes = netstat -ano | findstr :3001
if ($processes) {
    $pids = $processes | ForEach-Object { ($_ -split '\s+')[-1] } | Sort-Object -Unique
    foreach ($pid in $pids) {
        if ($pid -and $pid -ne "0") {
            try {
                Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
                Write-Host "✅ Proceso $pid detenido" -ForegroundColor Green
            } catch {
                Write-Host "⚠️ No se pudo detener proceso $pid" -ForegroundColor Yellow
            }
        }
    }
}

# Esperar un momento
Start-Sleep -Seconds 2

# Iniciar el servidor de desarrollo
Write-Host "🚀 Iniciando servidor en puerto 3001..." -ForegroundColor Green
Write-Host "📱 URL: http://localhost:3001/" -ForegroundColor Cyan
Write-Host "⏹️ Presiona Ctrl+C para detener" -ForegroundColor Yellow

npm run dev -- --port 3001
