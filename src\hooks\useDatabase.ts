
import { useState, useEffect, useCallback } from 'react';
import { databaseService } from '../services/DatabaseService';

interface UseDatabaseReturn {
  data: any[] | null;
  loading: boolean;
  error: string | null;
  execute: (query: string, params?: any[]) => Promise<any>;
  refresh: () => Promise<void>;
  isConnected: boolean;
}

export const useDatabase = (initialQuery?: string, initialParams?: any[]): UseDatabaseReturn => {
  const [data, setData] = useState<any[] | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  // Test connection on mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const connected = await databaseService.testConnection();
        setIsConnected(connected);
      } catch (err) {
        setIsConnected(false);
        setError('Failed to connect to database');
      }
    };
    
    checkConnection();
  }, []);

  // Execute query function
  const execute = useCallback(async (query: string, params: any[] = []) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await databaseService.query(query, params);
      setData(result);
      return result;
    } catch (err: any) {
      const errorMessage = err?.message || 'Database query failed';
      setError(errorMessage);
      console.error('Database error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh function to re-execute initial query
  const refresh = useCallback(async () => {
    if (initialQuery) {
      await execute(initialQuery, initialParams || []);
    }
  }, [execute, initialQuery, initialParams]);

  // Execute initial query on mount
  useEffect(() => {
    if (initialQuery && isConnected) {
      execute(initialQuery, initialParams || []);
    }
  }, [initialQuery, initialParams, execute, isConnected]);

  return {
    data,
    loading,
    error,
    execute,
    refresh,
    isConnected
  };
};

export default useDatabase;
