{"name": "fuse-react-app", "version": "16.0.0", "private": true, "type": "module", "dependencies": {"@aws-amplify/auth": "6.12.2", "@aws-amplify/ui-react": "6.11.0", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.7", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/react": "6.1.17", "@fullcalendar/timegrid": "6.1.17", "@hello-pangea/dnd": "18.0.1", "@hookform/resolvers": "5.0.1", "@mui/base": "5.0.0-beta.70", "@mui/icons-material": "7.2.0", "@mui/lab": "7.0.0-beta.14", "@mui/material": "7.2.0", "@mui/system": "7.2.0", "@mui/utils": "7.2.0", "@mui/x-data-grid": "8.7.0", "@mui/x-date-pickers": "8.7.0", "@popperjs/core": "2.11.8", "@react-google-maps/api": "2.20.6", "@react-spring/web": "9.7.5", "@tanstack/react-query": "^5.74.7", "@tanstack/react-query-devtools": "^5.74.7", "@tiptap/extension-highlight": "2.11.7", "@tiptap/extension-image": "2.11.7", "@tiptap/extension-link": "2.11.7", "@tiptap/extension-subscript": "2.11.7", "@tiptap/extension-superscript": "2.11.7", "@tiptap/extension-task-item": "2.11.7", "@tiptap/extension-task-list": "2.11.7", "@tiptap/extension-text-align": "2.11.7", "@tiptap/extension-typography": "2.11.7", "@tiptap/extension-underline": "2.11.7", "@tiptap/pm": "2.11.7", "@tiptap/react": "2.11.7", "@tiptap/starter-kit": "2.11.7", "apexcharts": "4.5.0", "autosuggest-highlight": "3.3.4", "aws-amplify": "6.14.2", "chance": "1.1.12", "clsx": "2.1.1", "core-js": "3.41.0", "crypto-js": "4.2.0", "date-fns": "4.1.0", "firebase": "11.6.0", "history": "5.3.0", "i18next": "25.0.1", "jwt-decode": "3.1.2", "keycode": "2.2.1", "ky": "1.8.1", "lodash": "4.17.21", "marked": "4.3.0", "material-react-table": "3.2.1", "material-ui-popup-state": "5.3.6", "mobile-detect": "1.4.5", "mobx": "6.13.7", "moment": "2.30.1", "motion": "12.20.5", "msw": "2.7.5", "mysql2": "^3.14.3", "notistack": "3.0.2", "perfect-scrollbar": "1.5.6", "prismjs": "1.30.0", "qs": "6.14.0", "react": "19.1.0", "react-apexcharts": "1.7.0", "react-app-alias": "2.2.2", "react-autosuggest": "10.1.0", "react-dom": "19.1.0", "react-draggable": "4.4.6", "react-hook-form": "7.56.0", "react-i18next": "15.4.1", "react-imask": "7.6.1", "react-number-format": "5.4.4", "react-popper": "2.3.0", "react-router": "7.5.2", "react-router-dom": "^7.8.2", "react-swipeable": "7.0.2", "react-transition-group": "4.4.5", "react-virtuoso": "4.12.6", "react-window": "1.8.11", "redoc": "2.5.0", "styled-components": "6.1.17", "stylis": "4.3.6", "stylis-plugin-rtl": "2.1.1", "type-fest": "4.40.0", "uuid": "11.1.0", "zod": "3.24.3"}, "peerDependencies": {"postcss": "8.5.3", "react": "19.1.0", "react-dom": "19.1.0"}, "overrides": {"react": "19.1.0", "react-dom": "19.1.0", "semver": "7.5.4"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.0", "@hookform/devtools": "4.4.0", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/typography": "0.5.16", "@tailwindcss/vite": "4.1.4", "@types/autosuggest-highlight": "3.2.3", "@types/crypto-js": "4.2.2", "@types/lodash": "4.17.16", "@types/marked": "4.3.2", "@types/node": "22.14.1", "@types/prismjs": "1.26.5", "@types/qs": "6.9.18", "@types/react": "19.1.2", "@types/react-autosuggest": "10.1.11", "@types/react-dom": "19.1.2", "@types/styled-components": "5.1.34", "@types/stylis": "4.2.7", "@typescript-eslint/eslint-plugin": "8.30.1", "@vitejs/plugin-react": "4.4.1", "cross-env": "^10.0.0", "eslint": "9.25.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "eslint-plugin-unused-imports": "4.1.4", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "promise": "8.3.0", "sass-embedded": "1.86.3", "tailwindcss": "4.1.4", "typescript": "5.8.3", "typescript-eslint": "8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "4.3.0", "vite-tsconfig-paths": "5.1.4"}, "scripts": {"start": "npm run dev", "dev": "vite --host", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 tsc && vite build", "build:win": "set NODE_OPTIONS=--max-old-space-size=4096 && tsc && vite build", "build:vps": "cross-env NODE_OPTIONS=--max-old-space-size=1024 tsc --noEmit false && vite build", "dev:vps": "cross-env NODE_OPTIONS=--max-old-space-size=1024 vite --host 0.0.0.0 --port 3002", "lint": "eslint ./src --config ./eslint.config.mjs", "lint:fix": "eslint --fix ./src --config ./eslint.config.mjs", "preview": "vite preview", "build-docs": "npx tsx  \"./src/app/(public)/documentation/lib/utils/build-mui-docs.ts\"", "postinstall": "node src/utils/node-scripts/fuse-react-message.js", "poststart": "node src/utils/node-scripts/fuse-react-message.js", "audit": "npm audit --production"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 3 safari version"]}, "msw": {"workerDirectory": ["public"]}}