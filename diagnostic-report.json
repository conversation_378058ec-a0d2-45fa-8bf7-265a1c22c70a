{"diagnostic_report": {"timestamp": "2025-08-29T03:24:00Z", "project": "fuse-react", "build_status": "SUCCESS", "development_server": {"status": "RUNNING", "url": "http://localhost:3000", "vite_version": "6.3.5"}, "critical_issues": [{"type": "API_CONNECTION_FAILURE", "severity": "HIGH", "description": "Multiple API endpoints failing to connect", "affected_endpoints": ["http://localhost:3001/api/mock/notifications", "http://localhost:3001/api/mock/project-dashboard/widgets", "http://localhost:3001/api/mock/messenger/profiles/*", "http://localhost:3001/api/mock/messenger/chat-list", "http://localhost:3001/api/mock/messenger/contacts", "http://localhost:3001/api/mock/project-dashboard/projects"], "error": "net::ERR_FAILED", "impact": "Application data not loading properly", "root_cause": "API server expected on port 3001 but not running", "recommendation": "Configure API base URL or start mock API server on port 3001"}, {"type": "VITE_WEBSOCKET_ERROR", "severity": "MEDIUM", "description": "WebSocket connection failure for hot module reloading", "error": "WebSocket connection to 'ws://localhost:443/?token=w8f16UoS_u1B' failed", "impact": "Hot module reloading may not work properly", "recommendation": "Check Vite configuration for WebSocket settings"}], "warnings": [{"type": "BUILD_CHUNK_SIZE", "message": "Some chunks are larger than 500 kB after minification", "impact": "Potential performance issues", "recommendation": "Consider code splitting and dynamic imports"}, {"type": "SWIPER_LOOP_WARNING", "message": "The number of slides is not enough for loop mode", "impact": "UI component behavior may be affected", "recommendation": "Review Swiper component configuration"}, {"type": "PROTECTED_AUDIENCE_ATTESTATION", "message": "Attestation check for Protected Audience failed", "impact": "Ad-related functionality may not work", "recommendation": "Review Google Ads configuration if needed"}], "console_errors": [{"type": "REFERENCE_ERROR", "message": "ReferenceError: k is not defined", "source": "https://www.googletagmanager.com/gt...", "impact": "Third-party script error"}, {"type": "NETWORK_ERROR", "message": "Failed to load resource: net::ERR_FAILED", "count": 50, "sources": ["localhost:3001/api/mock/*"]}], "msw_status": {"enabled": true, "worker_url": "http://localhost:3000/mockServiceWorker.js", "intercept_pattern": "/api/mock/*", "status": "ACTIVE", "client_id": "511de6ea-f2a3-462c-990a-cec10174c390"}, "configuration_analysis": {"api_base_url": "http://localhost:3001", "current_origin": "http://localhost:3000", "vite_server": "localhost:3000", "expected_api_server": "localhost:3001", "mismatch": true, "issue": "API server expected on different port than development server"}, "recommendations": [{"priority": "HIGH", "category": "API_CONFIGURATION", "action": "Fix API configuration", "details": ["Check .env files for VITE_API_BASE_URL configuration", "Ensure API server is running on localhost:3001", "Consider configuring MSW to handle all API calls locally", "Review src/utils/api.ts for API base URL configuration"]}, {"priority": "MEDIUM", "category": "PERFORMANCE", "action": "Optimize bundle size", "details": ["Implement code splitting for large components", "Use dynamic imports for rarely used features", "Review build.rollupOptions.output.manualChunks configuration"]}, {"priority": "LOW", "category": "DEVELOPMENT_EXPERIENCE", "action": "Fix HMR WebSocket issues", "details": ["Review Vite WebSocket configuration", "Check if port 443 conflicts exist", "Consider updating Vite configuration for WebSocket settings"]}], "app_functionality": {"status": "PARTIALLY_FUNCTIONAL", "ui_loading": "SUCCESS", "navigation": "SUCCESS", "theming": "SUCCESS", "authentication": "UNKNOWN", "data_loading": "FAILED", "real_time_features": "FAILED"}, "next_steps": ["1. Check and configure API base URL in environment files", "2. Start or configure API server on localhost:3001", "3. Test API endpoints manually to verify connectivity", "4. Review MSW configuration for complete local mocking", "5. Implement code splitting for performance optimization"]}}