# 🔄 Guía de Actualización Manual del VPS

## Comandos para actualizar desde GitHub

### 1. Conectarse al VPS
```bash
ssh -p 22222 root@**************
```

### 2. Ir al directorio del proyecto
```bash
cd /var/www/fuse-react
```

### 3. Detener el servidor actual
```bash
pkill -f "vite --host"
```

### 4. Actualizar código desde GitHub
```bash
git fetch origin
git reset --hard origin/vitejs-demo
git clean -fd
```

### 5. Instalar dependencias nuevas (si las hay)
```bash
npm install
```

### 6. Reiniciar servidor
```bash
NODE_OPTIONS="--max-old-space-size=1024" npm run dev
```

## Script Automático

También puedes usar el script automático:
```bash
chmod +x deploy-to-vps.sh
./deploy-to-vps.sh
```

## Verificar que funciona

- URL: http://**************:3000/
- Logs: `tail -f /var/log/fuse-react.log`

## Notas Importantes

- ⚠️ **Autenticación deshabilitada**: Actualmente la app no requiere login
- 🔄 **Para habilitar auth**: Cambiar `defaultAuth: null` a `defaultAuth: ['admin']` en `src/configs/settingsConfig.ts`
- 🚀 **Servidor de desarrollo**: Usando Vite dev server en puerto 3000
