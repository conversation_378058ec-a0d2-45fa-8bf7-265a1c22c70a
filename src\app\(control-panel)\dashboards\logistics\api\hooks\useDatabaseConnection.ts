import { useState, useEffect } from 'react';
import { logisticsApiService, ConnectionStatus, QueryResult } from '../services/logisticsApiService';

interface DatabaseConnectionState {
  isConnected: boolean | null;
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
  message?: string;
  connectionDetails?: any;
  modelData?: any[];
  isLoadingModelData: boolean;
}

/**
 * Hook para validar la conexión a la base de datos MySQL
 */
export const useDatabaseConnection = () => {
  const [state, setState] = useState<DatabaseConnectionState>({
    isConnected: null,
    isLoading: true,
    error: null,
    lastChecked: null,
    message: undefined,
    connectionDetails: null,
    modelData: [],
    isLoadingModelData: false,
  });

  const testDatabaseConnection = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const connectionStatus: ConnectionStatus = await logisticsApiService.testDatabaseConnection();
      const databaseInfo = await logisticsApiService.getDatabaseInfo();
      
      setState({
        isConnected: connectionStatus.isConnected,
        isLoading: false,
        error: connectionStatus.error || null,
        message: connectionStatus.message,
        lastChecked: new Date(connectionStatus.timestamp),
        connectionDetails: databaseInfo,
        modelData: [],
        isLoadingModelData: false,
      });
      return connectionStatus.isConnected;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      setState({
        isConnected: false,
        isLoading: false,
        error: errorMessage,
        lastChecked: new Date(),
        message: 'Error al conectar con la base de datos',
        connectionDetails: null,
        modelData: [],
        isLoadingModelData: false,
      });
      return false;
    }
  };

  const resetConnection = () => {
    setState({
      isConnected: null,
      isLoading: false,
      error: null,
      lastChecked: null,
      message: undefined,
      connectionDetails: null,
      modelData: [],
      isLoadingModelData: false,
    });
  };

  const fetchModelData = async () => {
    setState(prev => ({ ...prev, isLoadingModelData: true }));
    
    try {
      const result = await logisticsApiService.getModelSeniJuniorData();
      
      setState(prev => ({
        ...prev,
        modelData: result.success ? result.results : [],
        isLoadingModelData: false,
        error: result.success ? null : (result.message || 'Error al obtener datos del modelo'),
      }));
      
      return result.success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      
      setState(prev => ({
        ...prev,
        modelData: [],
        isLoadingModelData: false,
        error: errorMessage,
      }));
      
      return false;
    }
  };

  useEffect(() => {
    testDatabaseConnection().then(connected => {
      if (connected) {
        fetchModelData();
      }
    });
  }, []);

  return {
    ...state,
    testConnection: testDatabaseConnection,
    resetConnection,
    retry: testDatabaseConnection,
    fetchModelData,
  };
};