
import React, { useState } from 'react';
import {
  Paper,
  Typography,
  Button,
  TextField,
  Box,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { useDatabase } from '../hooks/useDatabase';

const DatabaseExample: React.FC = () => {
  const [query, setQuery] = useState<string>('SHOW TABLES');
  const { data, loading, error, execute, isConnected } = useDatabase();

  const handleExecuteQuery = async () => {
    try {
      await execute(query);
    } catch (err) {
      console.error('Query execution failed:', err);
    }
  };

  const handleTestConnection = async () => {
    try {
      await execute('SELECT 1 as test');
    } catch (err) {
      console.error('Connection test failed:', err);
    }
  };

  return (
    <Paper className="p-6 m-4">
      <Typography variant="h5" className="mb-4">
        MySQL Database Connection Example
      </Typography>

      {/* Connection Status */}
      <Alert 
        severity={isConnected ? 'success' : 'error'} 
        className="mb-4"
      >
        Database Connection: {isConnected ? 'Connected' : 'Disconnected'}
      </Alert>

      {/* Query Input */}
      <Box className="mb-4">
        <TextField
          fullWidth
          multiline
          rows={3}
          label="SQL Query"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          variant="outlined"
          className="mb-2"
        />
        <Box className="flex gap-2">
          <Button
            variant="contained"
            onClick={handleExecuteQuery}
            disabled={loading || !isConnected}
            startIcon={loading && <CircularProgress size={16} />}
          >
            Execute Query
          </Button>
          <Button
            variant="outlined"
            onClick={handleTestConnection}
            disabled={loading}
          >
            Test Connection
          </Button>
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" className="mb-4">
          Error: {error}
        </Alert>
      )}

      {/* Results Display */}
      {data && (
        <Box>
          <Typography variant="h6" className="mb-2">
            Query Results ({Array.isArray(data) ? data.length : 1} rows)
          </Typography>
          
          {Array.isArray(data) && data.length > 0 ? (
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    {Object.keys(data[0]).map((column) => (
                      <TableCell key={column} sx={{ fontWeight: 'bold' }}>
                        {column}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.map((row, index) => (
                    <TableRow key={index}>
                      {Object.values(row).map((value: any, cellIndex) => (
                        <TableCell key={cellIndex}>
                          {value?.toString() || 'NULL'}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No data returned or empty result set
            </Typography>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default DatabaseExample;
