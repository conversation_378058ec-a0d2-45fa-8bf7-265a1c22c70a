import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgrPlugin from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import tailwindcss from "@tailwindcss/vite";

// https://vitejs.dev/config/
export default defineConfig({
	plugins: [
		react({
			jsxImportSource: '@emotion/react'
		}),
		tsconfigPaths({
			parseNative: false
		}),
		svgrPlugin(),
		{
			name: 'custom-hmr-control',
			handleHotUpdate({ file, server }) {
				if (file.includes('src/app/configs/')) {
					server.ws.send({
						type: 'full-reload'
					});
					return [];
				}
			}
		},
		tailwindcss(),
	],
	build: {
		outDir: 'build',
		sourcemap: false,
		rollupOptions: {
			output: {
				manualChunks: {
					vendor: ['react', 'react-dom'],
					mui: ['@mui/material', '@mui/icons-material', '@mui/system'],
					emotion: ['@emotion/react', '@emotion/styled', '@emotion/cache'],
					utils: ['lodash', 'date-fns', 'uuid']
				}
			}
		},
		chunkSizeWarningLimit: 1000
	},
	server: {
		host: '0.0.0.0',
		port: 3000,
		strictPort: true,
		allowedHosts: ['955d316d-eba8-4ff3-997f-b98bbb856aea-00-1llqw0xsc9c.worf.replit.dev', 'localhost', '.replit.dev', '**************'],
		hmr: {
			clientPort: process.env.NODE_ENV === 'production' ? 443 : undefined,
			host: 'localhost'
		},
		cors: true
	},
	define: {
		'import.meta.env.VITE_PORT': JSON.stringify(process.env.PORT || 3000),
		global: 'window'
	},
	resolve: {
		alias: {
			'@': '/src',
			'@fuse': '/src/@fuse',
			'@history': '/src/@history',
			'@lodash': '/src/@lodash',
			'@mock-api': '/src/@mock-api',
			'@schema': '/src/@schema',
			'app/store': '/src/app/store',
			'app/shared-components': '/src/app/shared-components',
			'app/configs': '/src/app/configs',
			'app/theme-layouts': '/src/app/theme-layouts',
			'app/AppContext': '/src/app/AppContext'
		}
	},
	optimizeDeps: {
		include: [
			'@mui/icons-material',
			'@mui/material',
			'@mui/base',
			'@mui/system',
			'@mui/utils',
			'@emotion/cache',
			'@emotion/react',
			'@emotion/styled',
			'date-fns',
			'lodash'
		],
		exclude: ['@mui/styles'],
		esbuildOptions: {
			loader: {
				'.js': 'jsx'
			}
		}
	}
});
